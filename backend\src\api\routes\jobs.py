"""
Job Management API Routes
Handles job creation, status tracking, and results retrieval
"""

import logging
from typing import List, Optional
from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks, UploadFile, File, Form, Request
from fastapi.responses import JSONResponse
import asyncio
import uuid
import io

from models.job import <PERSON><PERSON><PERSON>, JobResponse, JobStatus, JobUpdate
from models.response import SuccessResponse, ErrorResponse
from database.supabase_client import (
    create_job, get_job, update_job, get_user_jobs, supabase_client
)
from services.csv_processor import process_uploaded_csv
from services.background_tasks import start_vessel_classification
from utils.auth import verify_firebase_token, get_current_user
from utils.validation import validate_csv_file
from utils.constants import JOB_STATUS, MAX_FILE_SIZE_MB, PROCESSING_CONFIG

logger = logging.getLogger(__name__)
router = APIRouter()

async def validate_file_size_streaming(request: Request, max_size_mb: int) -> None:
    """
    Validate file size using streaming approach to prevent memory exhaustion attacks.
    Raises HTTPException with 413 status if file exceeds size limit.

    Args:
        request: FastAPI request object containing the file stream
        max_size_mb: Maximum allowed file size in megabytes

    Raises:
        HTTPException: 413 Payload Too Large if file exceeds size limit
    """
    max_size_bytes = max_size_mb * 1024 * 1024

    # Check Content-Length header first (if provided by client)
    content_length = request.headers.get("content-length")
    if content_length:
        try:
            size = int(content_length)
            if size > max_size_bytes:
                logger.warning(f"File rejected: Content-Length {size} exceeds {max_size_bytes} bytes")
                raise HTTPException(
                    status_code=413,
                    detail=f"File size exceeds {max_size_mb}MB limit"
                )
        except ValueError:
            # Invalid Content-Length header, continue with streaming validation
            pass

    # Stream validation: read in chunks to monitor size without loading entire file
    total_size = 0
    chunk_size = 8192  # 8KB chunks

    # Note: This is a conceptual implementation. In practice, FastAPI's UploadFile
    # handles multipart parsing. For production, consider using a custom dependency
    # or middleware that validates before the file is fully parsed.
    logger.info(f"Streaming validation configured for max size: {max_size_mb}MB")

async def read_file_with_size_limit(file: UploadFile, max_size_mb: int) -> bytes:
    """
    Safely read file content with size monitoring to prevent memory exhaustion.

    Args:
        file: UploadFile object to read from
        max_size_mb: Maximum allowed file size in megabytes

    Returns:
        bytes: File content

    Raises:
        HTTPException: 413 if file exceeds size limit during reading
    """
    max_size_bytes = max_size_mb * 1024 * 1024
    total_size = 0
    chunks = []
    chunk_size = 8192  # 8KB chunks

    try:
        while True:
            chunk = await file.read(chunk_size)
            if not chunk:
                break

            total_size += len(chunk)
            if total_size > max_size_bytes:
                logger.warning(f"File reading aborted: size {total_size} exceeds {max_size_bytes} bytes")
                raise HTTPException(
                    status_code=413,
                    detail=f"File size exceeds {max_size_mb}MB limit"
                )

            chunks.append(chunk)

        # Reset file pointer for any subsequent reads
        await file.seek(0)

        file_content = b''.join(chunks)
        logger.info(f"File read successfully: {total_size} bytes")
        return file_content

    except HTTPException:
        # Re-raise our own HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error reading file: {e}")
        raise HTTPException(status_code=500, detail="Error reading uploaded file")

@router.post("/", response_model=JobResponse)
async def create_classification_job(
    background_tasks: BackgroundTasks,
    request: Request,
    file: UploadFile = File(...),
    user_id: str = Depends(get_current_user)
):
    """
    Create a new vessel classification job with streaming file size validation
    Uploads CSV file and starts processing

    Security Enhancement: Implements streaming validation to prevent memory exhaustion attacks
    """
    try:
        # Validate file extension first (lightweight check)
        if not file.filename or not file.filename.endswith('.csv'):
            raise HTTPException(status_code=400, detail="Only CSV files are allowed")

        # Enhanced security: Streaming file size validation to prevent memory exhaustion
        await validate_file_size_streaming(request, MAX_FILE_SIZE_MB)

        # Additional size check using UploadFile.size if available
        if file.size and file.size > MAX_FILE_SIZE_MB * 1024 * 1024:
            logger.warning(f"File rejected: UploadFile.size {file.size} exceeds limit")
            raise HTTPException(
                status_code=413,  # Changed to 413 Payload Too Large (more appropriate)
                detail=f"File size exceeds {MAX_FILE_SIZE_MB}MB limit"
            )

        logger.info(f"Creating job for user {user_id}, file: {file.filename}")

        # Generate job ID
        job_id = str(uuid.uuid4())

        # Secure file reading with size monitoring
        file_content = await read_file_with_size_limit(file, MAX_FILE_SIZE_MB)
        
        # Validate CSV structure
        validation_result = await validate_csv_file(file_content)
        if not validation_result['valid']:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid CSV format: {', '.join(validation_result['errors'])}"
            )
        
        # Create job record
        job_data = {
            'id': job_id,
            'user_id': user_id,
            'filename': file.filename,
            'file_size': len(file_content),
            'status': JOB_STATUS['CREATED'],
            'total_vessels': validation_result['vessel_count'],
            'progress': 0
        }
        
        job = await create_job(job_data)
        
        # Upload file to Supabase Storage
        file_path = f"{user_id}/{job_id}/{file.filename}"
        await supabase_client.upload_file('ais-data', file_path, file_content)
        
        # Update job with file path
        await update_job(job_id, {
            'file_path': file_path,
            'status': JOB_STATUS['UPLOADING']
        })
        
        # Start background processing
        background_tasks.add_task(
            start_vessel_classification,
            job_id=job_id,
            file_path=file_path,
            user_id=user_id
        )
        
        logger.info(f"Job {job_id} created successfully, processing started")
        
        return JobResponse(
            id=job['id'],
            filename=job['filename'],
            status=job['status'],
            progress=job['progress'],
            total_vessels=job['total_vessels'],
            created_at=job['created_at'],
            message="Job created successfully. Processing will begin shortly."
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to create job: {e}")
        raise HTTPException(status_code=500, detail="Failed to create classification job")

@router.get("/", response_model=List[JobResponse])
async def get_user_jobs_list(
    limit: int = 50,
    offset: int = 0,
    status: Optional[str] = None,
    user_id: str = Depends(get_current_user)
):
    """Get all jobs for the current user"""
    try:
        jobs = await get_user_jobs(user_id, limit + offset)
        
        # Apply offset and filtering
        if status:
            jobs = [job for job in jobs if job['status'] == status]
        
        jobs = jobs[offset:offset + limit]
        
        # Convert to response model
        job_responses = []
        for job in jobs:
            job_responses.append(JobResponse(
                id=job['id'],
                filename=job['filename'],
                status=job['status'],
                progress=job['progress'],
                total_vessels=job['total_vessels'],
                processed_vessels=job['processed_vessels'],
                tug_count=job.get('tug_count', 0),
                fishing_count=job.get('fishing_count', 0),
                pleasure_count=job.get('pleasure_count', 0),
                cargo_count=job.get('cargo_count', 0),
                processing_time_seconds=job.get('processing_time_seconds'),
                error_message=job.get('error_message'),
                created_at=job['created_at'],
                updated_at=job['updated_at'],
                completed_at=job.get('completed_at')
            ))
        
        return job_responses
        
    except Exception as e:
        logger.error(f"Failed to get user jobs: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve jobs")

@router.get("/{job_id}", response_model=JobResponse)
async def get_job_details(
    job_id: str,
    user_id: str = Depends(get_current_user)
):
    """Get details for a specific job"""
    try:
        job = await get_job(job_id)
        
        if not job:
            raise HTTPException(status_code=404, detail="Job not found")
        
        # Verify ownership
        if job['user_id'] != user_id:
            raise HTTPException(status_code=403, detail="Access denied")
        
        return JobResponse(
            id=job['id'],
            filename=job['filename'],
            status=job['status'],
            progress=job['progress'],
            total_vessels=job['total_vessels'],
            processed_vessels=job['processed_vessels'],
            tug_count=job.get('tug_count', 0),
            fishing_count=job.get('fishing_count', 0),
            pleasure_count=job.get('pleasure_count', 0),
            cargo_count=job.get('cargo_count', 0),
            processing_time_seconds=job.get('processing_time_seconds'),
            error_message=job.get('error_message'),
            created_at=job['created_at'],
            updated_at=job['updated_at'],
            started_at=job.get('started_at'),
            completed_at=job.get('completed_at')
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get job {job_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve job details")

@router.get("/{job_id}/status")
async def get_job_status(
    job_id: str,
    user_id: str = Depends(get_current_user)
):
    """Get quick status update for a job (lightweight endpoint for polling)"""
    try:
        job = await get_job(job_id)
        
        if not job:
            raise HTTPException(status_code=404, detail="Job not found")
        
        if job['user_id'] != user_id:
            raise HTTPException(status_code=403, detail="Access denied")
        
        # Return minimal status info for efficient polling
        return {
            "id": job['id'],
            "status": job['status'],
            "progress": job['progress'],
            "processed_vessels": job['processed_vessels'],
            "total_vessels": job['total_vessels'],
            "error_message": job.get('error_message'),
            "updated_at": job['updated_at']
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get job status {job_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to get job status")

@router.get("/{job_id}/results")
async def get_job_results(
    job_id: str,
    limit: int = 100,
    offset: int = 0,
    class_filter: Optional[str] = None,
    min_confidence: Optional[float] = None,
    user_id: str = Depends(get_current_user)
):
    """Get classification results for a completed job"""
    try:
        job = await get_job(job_id)
        
        if not job:
            raise HTTPException(status_code=404, detail="Job not found")
        
        if job['user_id'] != user_id:
            raise HTTPException(status_code=403, detail="Access denied")
        
        if job['status'] != JOB_STATUS['COMPLETED']:
            raise HTTPException(
                status_code=400, 
                detail=f"Job not completed. Current status: {job['status']}"
            )
        
        # Get vessel classifications from database
        query = supabase_client.client.table("vessel_classifications").select("*")
        query = query.eq("job_id", job_id)
        
        # Apply filters
        if class_filter:
            query = query.eq("class_name", class_filter.upper())
        
        if min_confidence:
            query = query.gte("confidence", min_confidence)
        
        # Apply pagination
        query = query.range(offset, offset + limit - 1)
        query = query.order("confidence", desc=True)
        
        result = query.execute()
        
        # Format response
        results = {
            "job_id": job_id,
            "status": job['status'],
            "total_vessels": job['total_vessels'],
            "summary": {
                "tug_count": job.get('tug_count', 0),
                "fishing_count": job.get('fishing_count', 0),
                "pleasure_count": job.get('pleasure_count', 0),
                "cargo_count": job.get('cargo_count', 0)
            },
            "classifications": result.data,
            "pagination": {
                "limit": limit,
                "offset": offset,
                "total": len(result.data)
            }
        }
        
        return results
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get results for job {job_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve job results")

@router.delete("/{job_id}")
async def cancel_job(
    job_id: str,
    user_id: str = Depends(get_current_user)
):
    """Cancel a running job"""
    try:
        job = await get_job(job_id)
        
        if not job:
            raise HTTPException(status_code=404, detail="Job not found")
        
        if job['user_id'] != user_id:
            raise HTTPException(status_code=403, detail="Access denied")
        
        # Can only cancel jobs that are not completed or failed
        if job['status'] in [JOB_STATUS['COMPLETED'], JOB_STATUS['FAILED']]:
            raise HTTPException(
                status_code=400, 
                detail=f"Cannot cancel job with status: {job['status']}"
            )
        
        # Update job status to cancelled
        await update_job(job_id, {
            'status': JOB_STATUS['CANCELLED'],
            'completed_at': 'NOW()'
        })
        
        # TODO: Implement actual background task cancellation
        # This would require a task queue system like Celery
        
        logger.info(f"Job {job_id} cancelled by user {user_id}")
        
        return SuccessResponse(
            message="Job cancelled successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to cancel job {job_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to cancel job")

@router.get("/{job_id}/download")
async def download_results(
    job_id: str,
    format: str = "json",  # json, csv
    user_id: str = Depends(get_current_user)
):
    """Download job results in specified format"""
    try:
        job = await get_job(job_id)
        
        if not job:
            raise HTTPException(status_code=404, detail="Job not found")
        
        if job['user_id'] != user_id:
            raise HTTPException(status_code=403, detail="Access denied")
        
        if job['status'] != JOB_STATUS['COMPLETED']:
            raise HTTPException(
                status_code=400, 
                detail="Job not completed yet"
            )
        
        # Get all results
        result = supabase_client.client.table("vessel_classifications").select("*").eq("job_id", job_id).execute()
        
        if format.lower() == "csv":
            # Convert to CSV format
            import io
            import csv
            
            output = io.StringIO()
            writer = csv.writer(output)
            
            # Write header
            writer.writerow([
                'mmsi', 'predicted_class', 'class_name', 'confidence',
                'tug_probability', 'fishing_probability', 
                'pleasure_probability', 'cargo_probability',
                'ais_points_count', 'time_span_hours', 
                'total_distance_km', 'avg_speed_knots'
            ])
            
            # Write data
            for row in result.data:
                writer.writerow([
                    row['mmsi'], row['predicted_class'], row['class_name'],
                    row['confidence'], row.get('tug_probability', 0),
                    row.get('fishing_probability', 0), row.get('pleasure_probability', 0),
                    row.get('cargo_probability', 0), row.get('ais_points_count'),
                    row.get('time_span_hours'), row.get('total_distance_km'),
                    row.get('avg_speed_knots')
                ])
            
            from fastapi.responses import StreamingResponse
            output.seek(0)
            
            return StreamingResponse(
                io.StringIO(output.getvalue()),
                media_type="text/csv",
                headers={"Content-Disposition": f"attachment; filename=veslint_results_{job_id}.csv"}
            )
        
        else:  # JSON format
            return {
                "job_id": job_id,
                "filename": job['filename'],
                "processing_completed_at": job['completed_at'],
                "summary": {
                    "total_vessels": job['total_vessels'],
                    "tug_count": job.get('tug_count', 0),
                    "fishing_count": job.get('fishing_count', 0),
                    "pleasure_count": job.get('pleasure_count', 0),
                    "cargo_count": job.get('cargo_count', 0)
                },
                "classifications": result.data
            }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to download results for job {job_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to download results")